import React from 'react';
import { Clock, Video, Target, TrendingUp } from 'lucide-react';
import JobStatusBadge from './JobStatusBadge';
import PlatformStatusIcon from './PlatformStatusIcon';
import { Job } from '../types';
import { formatRelativeTime, extractVideoId } from '../lib/utils';

interface JobCardProps {
  job: Job;
  onClick: () => void;
}

const JobCard: React.FC<JobCardProps> = ({ job, onClick }) => {
  const getProgressColor = (progress: number) => {
    if (progress === 100) return 'bg-green-500';
    if (progress > 0) return 'bg-blue-500';
    return 'bg-gray-500';
  };

  const getViralityScore = () => {
    if (job.clips.length === 0) return null;
    const scores = job.clips
      .map(clip => clip.virality_score)
      .filter(score => score !== undefined) as number[];
    
    if (scores.length === 0) return null;
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  };

  const viralityScore = getViralityScore();

  const getPriorityIndicator = () => {
    if (job.status === 'active') return '🔥';
    if (job.status === 'completed') return '✅';
    if (job.status === 'failed') return '⚠️';
    return '⏳';
  };

  return (
    <div
      className="job-card cursor-pointer group relative overflow-hidden"
      onClick={onClick}
    >
      {/* Priority Indicator */}
      <div className="absolute top-2 left-2 text-lg z-10">
        {getPriorityIndicator()}
      </div>

      {/* Header */}
      <div className="flex items-start justify-between mb-3 pl-8">
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-lg truncate group-hover:text-primary transition-colors">
            {job.title}
          </h3>
          <p className="text-sm text-muted-foreground truncate">
            ID: {job.id.slice(-8)} • {extractVideoId(job.source_url)}
          </p>
        </div>
        <JobStatusBadge status={job.status} />
      </div>

      {/* Progress Bar */}
      {job.status === 'active' && (
        <div className="mb-3">
          <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
            <span>{job.current_stage || 'Processing...'}</span>
            <span>{job.progress_percent}%</span>
          </div>
          <div className="w-full bg-muted rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(job.progress_percent)}`}
              style={{ width: `${job.progress_percent}%` }}
            />
          </div>
        </div>
      )}

      {/* Metrics Grid */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        <div className="flex items-center space-x-2 text-sm">
          <Video className="w-4 h-4 text-blue-400" />
          <span className="text-muted-foreground">Clips:</span>
          <span className="font-medium">{job.total_clips}</span>
        </div>
        
        <div className="flex items-center space-x-2 text-sm">
          <Clock className="w-4 h-4 text-green-400" />
          <span className="text-muted-foreground">Created:</span>
          <span className="font-medium">{formatRelativeTime(job.created_at)}</span>
        </div>

        {viralityScore && (
          <div className="flex items-center space-x-2 text-sm">
            <TrendingUp className="w-4 h-4 text-purple-400" />
            <span className="text-muted-foreground">Viral Score:</span>
            <span className="font-medium text-purple-400">{viralityScore}</span>
          </div>
        )}

        {job.deployments.length > 0 && (
          <div className="flex items-center space-x-2 text-sm">
            <Target className="w-4 h-4 text-orange-400" />
            <span className="text-muted-foreground">Deployed:</span>
            <span className="font-medium">{job.deployments.length}</span>
          </div>
        )}
      </div>

      {/* Platform Deployments */}
      {job.deployments.length > 0 && (
        <div className="mb-3">
          <div className="text-xs text-muted-foreground mb-2">Platform Status:</div>
          <div className="flex space-x-2">
            {job.deployments.map((deployment) => (
              <PlatformStatusIcon
                key={deployment.platform}
                platform={deployment.platform}
                status={deployment.status === 'uploaded' ? 'connected' : 'error'}
              />
            ))}
          </div>
        </div>
      )}

      {/* Latest Process Activity */}
      {job.process_log.length > 0 && (
        <div className="border-t border-border pt-3">
          <div className="text-xs text-muted-foreground mb-1">Latest Activity:</div>
          <div className="flex items-start space-x-2">
            <div className="agent-avatar">
              {job.process_log[job.process_log.length - 1].agent.charAt(0)}
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-xs font-medium truncate">
                {job.process_log[job.process_log.length - 1].action}
              </div>
              <div className="text-xs text-muted-foreground truncate">
                {job.process_log[job.process_log.length - 1].message}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {job.status === 'failed' && job.error_message && (
        <div className="border-t border-border pt-3">
          <div className="text-xs text-red-400 mb-1">Error:</div>
          <div className="text-xs text-muted-foreground truncate">
            {job.error_message}
          </div>
        </div>
      )}

      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none" />
    </div>
  );
};

export default JobCard;
