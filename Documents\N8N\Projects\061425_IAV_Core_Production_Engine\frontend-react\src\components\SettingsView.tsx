import React from 'react';
import { RefreshCw, <PERSON>Link, <PERSON>tings, Wifi, WifiOff, AlertTriangle } from 'lucide-react';
import { Button } from './ui/Button';
import PlatformStatusIcon from './PlatformStatusIcon';
import { PlatformConnection, PLATFORM_LABELS, CONNECTION_STATUS_LABELS } from '../types';
import { formatRelativeTime } from '../lib/utils';

interface SettingsViewProps {
  connections: PlatformConnection[];
  onRefresh: () => void;
  onAuthorize: (platform: string) => void;
}

const SettingsView: React.FC<SettingsViewProps> = ({
  connections,
  onRefresh,
  onAuthorize,
}) => {
  const connectedCount = connections.filter(c => c.status === 'connected').length;
  const totalPlatforms = connections.length;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <Wifi className="w-5 h-5 text-green-400" />;
      case 'disconnected':
        return <WifiOff className="w-5 h-5 text-red-400" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      default:
        return <WifiOff className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'text-green-400';
      case 'disconnected':
        return 'text-red-400';
      case 'error':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            ⚙️ System Settings
          </h1>
          <p className="text-muted-foreground mt-1">
            Configure platform connections and system settings
          </p>
        </div>
        <Button variant="outline" onClick={onRefresh}>
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh Status
        </Button>
      </div>

      {/* Connection Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="metric-card">
          <div className="metric-value">{connectedCount}/{totalPlatforms}</div>
          <div className="metric-label flex items-center">
            <Wifi className="w-4 h-4 mr-1" />
            Platforms Online
          </div>
        </div>
        <div className="metric-card">
          <div className={`metric-value ${connectedCount === totalPlatforms ? 'text-green-400' : 'text-yellow-400'}`}>
            {connectedCount === totalPlatforms ? '100%' : Math.round((connectedCount / totalPlatforms) * 100) + '%'}
          </div>
          <div className="metric-label">System Readiness</div>
        </div>
        <div className="metric-card">
          <div className="metric-value text-blue-400">
            {connections.length > 0 ? formatRelativeTime(connections[0].last_checked) : 'Never'}
          </div>
          <div className="metric-label">Last Check</div>
        </div>
      </div>

      {/* Platform Connections */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2" />
          Platform Connections
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {connections.map((connection) => (
            <div key={connection.platform} className="glass-panel p-6">
              {/* Platform Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <PlatformStatusIcon
                    platform={connection.platform}
                    status={connection.status}
                    className="scale-125"
                  />
                  <div>
                    <h3 className="font-semibold text-lg">
                      {PLATFORM_LABELS[connection.platform]}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Social media platform integration
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(connection.status)}
                </div>
              </div>

              {/* Status Information */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status:</span>
                  <span className={`text-sm font-medium ${getStatusColor(connection.status)}`}>
                    {CONNECTION_STATUS_LABELS[connection.status]}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Last Checked:</span>
                  <span className="text-sm">{formatRelativeTime(connection.last_checked)}</span>
                </div>

                {connection.health_message && (
                  <div className="flex items-start justify-between">
                    <span className="text-sm text-muted-foreground">Message:</span>
                    <span className="text-sm text-right max-w-xs">
                      {connection.health_message}
                    </span>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Service URL:</span>
                  <span className="text-sm font-mono text-blue-400">
                    {connection.service_url.replace('http://', '')}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAuthorize(connection.platform)}
                  className="flex-1"
                >
                  <ExternalLink className="w-4 h-4 mr-1" />
                  {connection.status === 'connected' ? 'Re-authorize' : 'Connect'}
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.open(connection.service_url + '/health', '_blank')}
                  title="Test connection health"
                >
                  Test
                </Button>
              </div>

              {/* Connection Quality Indicator */}
              <div className="mt-4 pt-4 border-t border-border">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">Connection Quality:</span>
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((bar) => (
                      <div
                        key={bar}
                        className={`w-1 h-3 rounded-full ${
                          connection.status === 'connected' && bar <= 4
                            ? 'bg-green-400'
                            : connection.status === 'error' && bar <= 2
                            ? 'bg-yellow-400'
                            : 'bg-gray-600'
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* System Information */}
      <div>
        <h2 className="text-xl font-semibold mb-4">System Information</h2>
        
        <div className="glass-panel p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-3">Service Endpoints</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Jobs Coordinator:</span>
                  <span className="font-mono text-blue-400">localhost:8700</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">n8n Workflow:</span>
                  <span className="font-mono text-blue-400">localhost:8678</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Ollama AI:</span>
                  <span className="font-mono text-blue-400">localhost:11534</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Whisper ASR:</span>
                  <span className="font-mono text-blue-400">localhost:9100</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium mb-3">Quick Actions</h3>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('http://localhost:8678', '_blank')}
                  className="w-full justify-start"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Open n8n Workflow Editor
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('http://localhost:11534', '_blank')}
                  className="w-full justify-start"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Open Ollama API
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('http://localhost:8700/docs', '_blank')}
                  className="w-full justify-start"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  API Documentation
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* System Health */}
      <div>
        <h2 className="text-xl font-semibold mb-4">System Health</h2>

        <div className="glass-panel p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-3">Connection Status</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Platforms:</span>
                  <span className="font-medium">{connections.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Connected:</span>
                  <span className="font-medium text-green-400">{connectedCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Disconnected:</span>
                  <span className="font-medium text-red-400">{connections.length - connectedCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">System Status:</span>
                  <span className={`font-medium ${connectedPlatforms > 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {connectedPlatforms > 0 ? 'Operational' : 'Offline'}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-medium mb-3">System Actions</h3>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRefresh}
                  className="w-full justify-start"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh All Connections
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('http://localhost:8678', '_blank')}
                  className="w-full justify-start"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Open n8n Workflow Editor
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('http://localhost:8700/docs', '_blank')}
                  className="w-full justify-start"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  API Documentation
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsView;
