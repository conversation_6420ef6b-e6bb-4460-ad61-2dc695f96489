@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .job-card {
    @apply relative overflow-hidden rounded-lg border bg-card p-6 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-[1.02];
  }

  .job-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0 transition-opacity duration-300;
  }

  .job-card:hover::before {
    @apply opacity-100;
  }
  
  .status-badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .status-queued {
    @apply bg-yellow-500/20 text-yellow-400 border border-yellow-500/30;
  }
  
  .status-active {
    @apply bg-blue-500/20 text-blue-400 border border-blue-500/30 animate-pulse-glow;
  }
  
  .status-completed {
    @apply bg-green-500/20 text-green-400 border border-green-500/30;
  }
  
  .status-failed {
    @apply bg-red-500/20 text-red-400 border border-red-500/30;
  }
  
  .platform-icon {
    @apply w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold;
  }
  
  .platform-tiktok {
    @apply bg-pink-500 text-white;
  }
  
  .platform-instagram {
    @apply bg-gradient-to-br from-purple-500 to-pink-500 text-white;
  }
  
  .platform-youtube {
    @apply bg-red-500 text-white;
  }
  
  .platform-twitter {
    @apply bg-blue-400 text-white;
  }
  
  .connection-connected {
    @apply text-green-400;
  }
  
  .connection-disconnected {
    @apply text-red-400;
  }
  
  .connection-error {
    @apply text-yellow-400;
  }
  
  .glass-panel {
    @apply backdrop-blur-sm bg-white/5 border border-white/10 rounded-lg;
  }
  
  .command-button {
    @apply relative overflow-hidden rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-3 font-semibold text-white transition-all duration-300 hover:from-blue-700 hover:to-purple-700 hover:shadow-lg hover:shadow-blue-500/25;
  }
  
  .command-button::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300;
  }
  
  .command-button:hover::before {
    @apply opacity-100;
  }
  
  .metric-card {
    @apply glass-panel p-4 space-y-2;
  }
  
  .metric-value {
    @apply text-2xl font-bold text-primary;
  }
  
  .metric-label {
    @apply text-sm text-muted-foreground;
  }
  
  .agent-log-entry {
    @apply flex items-start space-x-3 p-3 rounded-lg bg-muted/30 border border-muted/50;
  }
  
  .agent-avatar {
    @apply w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-primary text-xs font-bold;
  }
  
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(75 85 99) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }

  /* Advanced Tesla/SpaceX inspired animations */
  .job-card-enter {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  .job-card-enter-active {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .job-card-exit {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  .job-card-exit-active {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
    transition: all 0.3s ease-in-out;
  }

  /* Holographic effect for active jobs */
  .holographic-border {
    position: relative;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    background-size: 200% 200%;
    animation: holographic-flow 3s ease-in-out infinite;
  }

  .holographic-border::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #3b82f6);
    background-size: 300% 300%;
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: holographic-border 4s linear infinite;
  }

  @keyframes holographic-flow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes holographic-border {
    0% { background-position: 0% 50%; }
    100% { background-position: 300% 50%; }
  }

  /* Neural network background pattern */
  .neural-bg {
    background-image:
      radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
    background-size: 100px 100px, 150px 150px, 200px 200px;
    animation: neural-drift 20s ease-in-out infinite;
  }

  @keyframes neural-drift {
    0%, 100% { background-position: 0% 0%, 0% 0%, 0% 0%; }
    33% { background-position: 30% 30%, -30% 30%, 15% -15%; }
    66% { background-position: -30% -30%, 30% -30%, -15% 15%; }
  }

  /* Command center glow effects */
  .command-glow {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .command-glow:hover {
    box-shadow:
      0 0 30px rgba(59, 130, 246, 0.5),
      0 0 60px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  /* Data stream effect */
  .data-stream {
    position: relative;
    overflow: hidden;
  }

  .data-stream::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(59, 130, 246, 0.2),
      transparent
    );
    animation: data-flow 2s infinite;
  }

  @keyframes data-flow {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  /* Quantum particle effect */
  .quantum-particles {
    position: relative;
  }

  .quantum-particles::before {
    content: '';
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(59, 130, 246, 0.6);
    border-radius: 50%;
    animation: quantum-float 3s ease-in-out infinite;
    top: 20%;
    left: 20%;
  }

  .quantum-particles::after {
    content: '';
    position: absolute;
    width: 1px;
    height: 1px;
    background: rgba(139, 92, 246, 0.8);
    border-radius: 50%;
    animation: quantum-float 4s ease-in-out infinite reverse;
    top: 70%;
    right: 30%;
  }

  @keyframes quantum-float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
    50% { transform: translateY(-10px) rotate(180deg); opacity: 1; }
  }

  /* Status pulse animations */
  .status-pulse-active {
    animation: status-pulse 2s ease-in-out infinite;
  }

  .status-pulse-error {
    animation: error-pulse 1s ease-in-out infinite;
  }

  @keyframes status-pulse {
    0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
    50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.3); }
  }

  @keyframes error-pulse {
    0%, 100% { box-shadow: 0 0 5px rgba(239, 68, 68, 0.5); }
    50% { box-shadow: 0 0 15px rgba(239, 68, 68, 0.8); }
  }
}
